name: Build Base Images

on:
  schedule:
    # Rebuild base images weekly on Sundays at 2 AM UTC
    - cron: '0 2 * * 0'
  workflow_dispatch:
    inputs:
      force_rebuild:
        description: 'Force rebuild all base images'
        required: false
        default: 'false'
        type: boolean
  push:
    paths:
      - '**/requirements*.txt'
      - '**/package.json'
      - '**/pnpm-lock.yaml'
      - 'docker/base-images/**'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build-python-base:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract Python dependencies hash
      id: deps-hash
      run: |
        # Create combined hash of all Python requirements
        HASH=$(cat ai-engine/requirements.txt backend/requirements.txt | sha256sum | cut -d' ' -f1 | head -c16)
        echo "hash=$HASH" >> $GITHUB_OUTPUT
        echo "Python deps hash: $HASH"
    
    - name: Check if base image exists
      id: check-image
      run: |
        IMAGE_TAG="${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/python-base:${{ steps.deps-hash.outputs.hash }}"
        if docker buildx imagetools inspect "$IMAGE_TAG" > /dev/null 2>&1; then
          echo "exists=true" >> $GITHUB_OUTPUT
          echo "Base image already exists: $IMAGE_TAG"
        else
          echo "exists=false" >> $GITHUB_OUTPUT
          echo "Base image needs to be built: $IMAGE_TAG"
        fi
    
    - name: Build and push Python base image
      if: steps.check-image.outputs.exists == 'false' || github.event.inputs.force_rebuild == 'true'
      uses: docker/build-push-action@v5
      with:
        context: .
        file: docker/base-images/Dockerfile.python-base
        push: true
        tags: |
          ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/python-base:${{ steps.deps-hash.outputs.hash }}
          ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/python-base:latest
        cache-from: type=gha,scope=python-base
        cache-to: type=gha,mode=max,scope=python-base
        platforms: linux/amd64

  build-node-base:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract Node dependencies hash
      id: deps-hash
      run: |
        HASH=$(sha256sum frontend/pnpm-lock.yaml | cut -d' ' -f1 | head -c16)
        echo "hash=$HASH" >> $GITHUB_OUTPUT
        echo "Node deps hash: $HASH"
    
    - name: Check if base image exists
      id: check-image
      run: |
        IMAGE_TAG="${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/node-base:${{ steps.deps-hash.outputs.hash }}"
        if docker buildx imagetools inspect "$IMAGE_TAG" > /dev/null 2>&1; then
          echo "exists=true" >> $GITHUB_OUTPUT
        else
          echo "exists=false" >> $GITHUB_OUTPUT
        fi
    
    - name: Build and push Node base image
      if: steps.check-image.outputs.exists == 'false' || github.event.inputs.force_rebuild == 'true'
      uses: docker/build-push-action@v5
      with:
        context: frontend
        file: docker/base-images/Dockerfile.node-base
        push: true
        tags: |
          ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/node-base:${{ steps.deps-hash.outputs.hash }}
          ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/node-base:latest
        cache-from: type=gha,scope=node-base
        cache-to: type=gha,mode=max,scope=node-base
        platforms: linux/amd64
